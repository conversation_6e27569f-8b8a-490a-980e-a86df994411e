<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">
import WrapperCard from '@/components/biz/card/wrapper-card.vue';
import Test from './test.vue';

interface Props {
  content?:Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  content: () => ({ }),
});

</script>


<template>
  <WrapperCard>
    <template #header>
      <Test />
    </template>

    {{ content }}

    <template #footer>
      333
    </template>
  </WrapperCard>
</template>


<style lang="less" scoped>
</style>
