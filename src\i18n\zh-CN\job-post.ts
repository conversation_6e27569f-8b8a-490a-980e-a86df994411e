export default {
  title: {
    add: '新增岗位',
    edit: '编辑岗位',
    reapply: '重新发起',
    detail: '岗位详情',
  },


  label: {
    status: '状态',
    publishStatus: '发布状态',
    createTime: '创建时间',

    postName: '岗位名称',
    postType: '岗位类型',
    researchDirection: '研究方向',
    sexRemand: '性别要求',
    neededMajor: '所需专业',
    numOfPerson: '需求人数',
    jobContent: '工作内容',
    practiceAddress: '实践地址',
    detailedAddress: '详细地址',
    deadline: '报名截止时间',
    otherRemand: '其他要求',
    publishType: '发布类型',

    postUnit: '岗位单位',
    companyProfile: '公司简介',
  },

  columns: {
    postName: '@:jobPost.label.postName',
    researchDirection: '@:jobPost.label.researchDirection',
    sexRemand: '@:jobPost.label.sexRemand',
    neededMajor: '@:jobPost.label.neededMajor',
    numOfPerson: '@:jobPost.label.numOfPerson',
    practiceAddress: '@:jobPost.label.practiceAddress',
    deadline: '@:jobPost.label.deadline',
    createTime: '@:jobPost.label.createTime',
    status: '@:jobPost.label.status',
    publishStatusAndTime: '发布状态/发布时间',
  },

  placeholder: {
    status: '@:jobPost.label.status',
    publishStatus: '@:jobPost.label.publishStatus',
    creStartTime: '创建开始时间',
    creEndTime: '创建结束时间',
    keyword: '请输入岗位名称',
    pleaseInput: '请填写',
    pleaseSelect: '请选择',

  },

  action: {
    edit: '@:common.action.edit',
    delete: '@:common.action.delete',
    export: '@:common.action.export',
    cancel: '@:common.action.cancel',

    reapply: '@:jobPost.title.reapply',
    add: '新增岗位',
    view: '查看',
    draft: '暂存草稿',
    submit: '提交',
  },

  modal: {
    delete: {
      title: '确认删除',
      content: '数据删除后将无法恢复，确认删除吗？',
    },
  },

  actionLog: {
    title: '申请日志',
    emptyText: '暂无{title}',
  },

};
