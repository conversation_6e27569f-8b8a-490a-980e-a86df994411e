<!-- stylelint-disable -->
<template>
  <div class="tags-grid">
    <div class="tag-item">
      Item One
    </div>
    <div class="tag-item">
      Item Two
    </div>
    <div class="tag-item">
      Item Three
    </div>
    <div class="tag-item">
      Item Four
    </div>
    <div class="tag-item">
      Item Five
    </div>
    <div class="ellipsis">
      ...
    </div>
  </div>
</template>

<style lang="less" scoped>
/* stylelint-disable */
.tags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(auto, max-content));
  gap: 8px;
  overflow: hidden;
  position: relative;

  // 限制最大列数，超出部分被隐藏
  grid-template-columns: repeat(4, minmax(auto, max-content)) auto;
}

.tag-item {
  padding: 6px 12px;
  background: #1890ff;
  color: white;
  border-radius: 4px;
  white-space: nowrap;
  font-size: 14px;
  border: 2px solid #1890ff;
}

.ellipsis {
  display: flex;
  align-items: center;
  color: #666;
  font-weight: bold;
  grid-column: -1;
}
</style>
