import type { InjectionKey } from 'vue';
import { RouterName as RN } from './router';


// 侧边栏菜单编码，键值对一对一，需包含子级菜单
// 不包含子级菜单关系，子级菜单关系由 SiderMenuItemsFlatMap 维护
// 存储所有的菜单编码
export const SiderMenuCodes = Object.freeze({
  /** 岗位浏览 */
  JobBrowsing:'Job-Browsing',
  /** 我的简历 */
  MyResume:'My-Resume',
});


export const SMC = SiderMenuCodes;

// 侧边栏菜单对照
// 存储所有一/二级菜单(编码)到路由配置的映射
// 子级节点由 children 控制，最多包含一级子菜单，全部总两级，超过两级会自动忽略
export const SiderMenu = Object.freeze(new Map([
  /** 岗位浏览 */
  [SMC.JobBrowsing, {
    routeName: RN.JobBrowsing,
  }],

  /** 我的简历 */
  [SMC.MyResume, {
    routeName: RN.MyResume,
  }],

]));

/**
 * 此文件定义the-root provided key
 */
type GetMenuName = {
  (fn: (SM: typeof SiderMenuCodes) => string): string;
};

export const MENU_NAME_INJECTION_KEY = Symbol('menuName') as InjectionKey<GetMenuName>;
