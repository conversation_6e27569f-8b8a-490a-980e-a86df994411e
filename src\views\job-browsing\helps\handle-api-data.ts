import _ from 'lodash';

import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { createFormModel } from './models';


/** 处理时间数据为 YYYY-MM-DD */
export const formateToDate = (date: string | Date) => {
  const t = namespaceT('dateFormat');
  return dateFormatSTZ(new Date(date), t('date'));
};


/** 处理详情数据 */
export const handleDetailData = (data) => {
  const cloneData = _.cloneDeep(data);

  cloneData.name1 = [cloneData.regionProvince, cloneData.regionCity];
  if (cloneData.regionDistrict) {
    cloneData.name1.push(cloneData.regionDistrict);
  }

  return _.pick(cloneData, Object.keys(createFormModel()));
};
