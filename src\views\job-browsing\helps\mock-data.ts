import { JobPostStatus } from '@/consts/job-post';

export interface JobBrowsingListItem {
  id: number;
  postName: string;
  postType: string;
  researchDirection: string;
  sexRemand: string;
  neededMajor: string;
  numOfPerson: number;
  practiceAddress: string;
  deadline: string;
  creTime: string;
  status: JobPostStatus;
  publishStatus?: string;
  publishTime?: string;
}

export const mockJobPostData: JobBrowsingListItem[] = [
  {
    id: 1,
    postName: '前端开发工程师',
    postType: 'internship',
    researchDirection: 'Web应用开发',
    sexRemand: '不限',
    neededMajor: '计算机科学与技术、软件工程',
    numOfPerson: 5,
    practiceAddress: '北京市海淀区中关村软件园',
    deadline: '2025-08-30T23:59:59',
    creTime: '2025-07-01T09:30:00',
    status: JobPostStatus.Passed,
    publishStatus: 'published',
    publishTime: '2025-07-05T10:00:00',
  },
  {
    id: 2,
    postName: '后端开发实习生',
    postType: 'internship',
    researchDirection: '分布式系统',
    sexRemand: '不限',
    neededMajor: '计算机科学与技术、软件工程、网络工程',
    numOfPerson: 3,
    practiceAddress: '上海市浦东新区张江高科技园区',
    deadline: '2025-09-15T23:59:59',
    creTime: '2025-07-10T14:20:00',
    status: JobPostStatus.Pending,
  },
  {
    id: 3,
    postName: '数据分析师',
    postType: 'fulltime',
    researchDirection: '大数据分析',
    sexRemand: '不限',
    neededMajor: '统计学、数学与应用数学、数据科学',
    numOfPerson: 2,
    practiceAddress: '深圳市南山区科技园',
    deadline: '2025-08-20T23:59:59',
    creTime: '2025-06-25T11:15:00',
    status: JobPostStatus.Passed,
    publishStatus: 'published',
    publishTime: '2025-06-30T09:00:00',
  },
  {
    id: 4,
    postName: '人工智能研究员',
    postType: 'research',
    researchDirection: '机器学习、深度学习',
    sexRemand: '不限',
    neededMajor: '人工智能、计算机科学、数学',
    numOfPerson: 1,
    practiceAddress: '杭州市西湖区阿里巴巴西溪园区',
    deadline: '2025-10-10T23:59:59',
    creTime: '2025-07-15T16:45:00',
    status: JobPostStatus.Draft,
  },
  {
    id: 5,
    postName: 'UI/UX设计师',
    postType: 'internship',
    researchDirection: '用户体验设计',
    sexRemand: '不限',
    neededMajor: '数字媒体技术、工业设计、视觉传达',
    numOfPerson: 2,
    practiceAddress: '广州市天河区珠江新城',
    deadline: '2025-09-05T23:59:59',
    creTime: '2025-07-05T13:20:00',
    status: JobPostStatus.Rejected,
  },
  {
    id: 6,
    postName: '产品经理',
    postType: 'fulltime',
    researchDirection: '产品规划与管理',
    sexRemand: '不限',
    neededMajor: '信息管理与信息系统、工商管理',
    numOfPerson: 1,
    practiceAddress: '北京市朝阳区望京SOHO',
    deadline: '2025-08-25T23:59:59',
    creTime: '2025-06-20T10:30:00',
    status: JobPostStatus.Passed,
    publishStatus: 'published',
    publishTime: '2025-06-22T14:00:00',
  },
  {
    id: 7,
    postName: '测试工程师',
    postType: 'internship',
    researchDirection: '软件测试',
    sexRemand: '不限',
    neededMajor: '计算机科学与技术、软件工程',
    numOfPerson: 3,
    practiceAddress: '成都市高新区天府软件园',
    deadline: '2025-09-30T23:59:59',
    creTime: '2025-07-12T09:15:00',
    status: JobPostStatus.Aborted,
    publishStatus: 'unpublished',
    publishTime: '2025-07-18T11:30:00',
  },
  {
    id: 8,
    postName: '运维工程师',
    postType: 'fulltime',
    researchDirection: '系统运维与监控',
    sexRemand: '男',
    neededMajor: '网络工程、信息安全、计算机科学与技术',
    numOfPerson: 2,
    practiceAddress: '武汉市光谷软件园',
    deadline: '2025-08-15T23:59:59',
    creTime: '2025-07-02T15:40:00',
    status: JobPostStatus.Passed,
    publishStatus: 'published',
    publishTime: '2025-07-03T09:00:00',
  },
];
