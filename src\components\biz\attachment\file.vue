<script setup lang="ts">
interface Props {
  fileName: string
}

defineProps<Props>();
</script>

<template>
  <div class="file">
    <a
      v-bind="$attrs"
      target="_blank"
    >
      {{ fileName }}
    </a>
  </div>
</template>

<style lang="less" scoped>
.file {
  display: flex;
  gap: 12px;
  align-items: center;
  word-break: break-word;

  &:not(:last-child) {
    margin-bottom: 12px;
  }

  a {
    flex: 1;
    overflow: hidden;
    color: #111;
    font-weight: 400;
    font-size: 13px;
    white-space: nowrap;
    text-overflow: ellipsis;

    &:hover {
      color: var(--primary-color);
    }
  }
}
</style>
