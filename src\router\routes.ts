import type { RouteRecordRaw } from 'vue-router';
import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import { Auth } from '@/config/auth';

import TheRoot from '@/components/the-root.vue';


export const routes = Object.freeze<RouteRecordRaw[]>([
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [

      /** 岗位浏览 */
      {
        path: 'job-browsing',
        name: RN.JobBrowsing,
        component: null,
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.JobBrowsing],
          baseAuthCodes: [Auth.JobBrowsing.View],
        },
      },

      /** 岗位浏览 详情 */
      {
        path: 'job-browsing/:id/detail',
        name: RN.JobBrowsingDetail,
        component: null,
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.JobBrowsing],
          baseAuthCodes: [Auth.JobBrowsing.View],
        },
      },

      /** 我的简历 */
      {
        path: 'my-resume',
        name: RN.MyResume,
        component: null,
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.MyResume],
          baseAuthCodes: [Auth.MyResume.View],
        },
      },

      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
]);
