<script setup lang="ts">
import { inject, reactive, ref, computed } from 'vue';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import DetailForm from './components/detail-form.vue';

import { MENU_NAME_INJECTION_KEY, type SiderMenuCodes } from '@/config/sider-menu';
import { namespaceT } from '@/helps/namespace-t';
import { useStudentPersonInfoUrl } from '@/uses/use-student-person-info-url';
import { openLink } from '@/utils/open-link';


const menuName = inject<GetMenuName>(MENU_NAME_INJECTION_KEY);

const loading = ref(false);
const t = namespaceT('myResume');
const model = reactive({});

const title = computed(() => {
  return menuName((SC:typeof SiderMenuCodes) => SC.MyResume);
});

const studentPersonalInfoUrl = useStudentPersonInfoUrl(6);

const onEdit = () => {
  // 跳转到学生档案应用 个人信息详情页
  // id？哪来？
  console.log('%c [  ]-30', 'font-size:13px; background:#f42be2; color:#ff6fff;', studentPersonalInfoUrl);
  // 无法正常打开？ ticket权限？
  openLink(studentPersonalInfoUrl);
};


</script>


<template>
  <div class="pima-form-page">
    <TitleBar
      :title="title"
    >
      <template #right>
        <Button
          class="pima-btn"
          type="primary"
          @click="onEdit"
        >
          {{ t('action.edit') }}
        </Button>
      </template>
    </TitleBar>
    <WrapperForm :loading="loading">
      <WrapperFormTitle :title="title">
        <DetailForm
          v-model="model"
        />
      </WrapperFormTitle>
    </WrapperForm>
  </div>
</template>
