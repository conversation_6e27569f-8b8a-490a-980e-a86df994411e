<script setup lang="ts">
import { ref } from 'vue';
import { isBefore, subDays } from 'date-fns';

import PimaInput from '@/components/common/pima-input.vue';
import SelectPostType from '@/components/biz/select/post-type.vue';
import RadioPublishType from '@/components/biz/radio/publish-type.vue';
import RadioSex from '@/components/biz/radio/sex.vue';
import NumberInput from '@/components/biz/number/number-input.vue';
import CascaderRegions from '@/components/biz/cascader/regions.vue';
import PimaRemoteEditor from '@/components/common/remote/pima-remote-editor.vue';
import SelectDate from '@/components/common/picker/date.vue';


import { InputType } from '@/consts/input-type';
import { namespaceT } from '@/helps/namespace-t';
import { createFormRules } from './helps/rules';
import { PublishType } from '@/consts/job-post';


const model = defineModel<Record<string, any>>();

const t = namespaceT('jobPost');
const rules = createFormRules();
const formRef = ref();

const validate = async () => {
  const flag = await formRef.value.validate();
  return flag;
};

const validateField = async <T=void>(fieldName:string, callback:()=>T) => {
  await formRef.value.validateField(fieldName, callback);
};

const resetFields = () => {
  formRef.value.resetFields();
};

/** 时间不小于当天时间 */
const disabledDate = (date:Date) => {
  return date && isBefore(date, subDays(new Date(), 1));
};


defineExpose({
  validate,
  validateField,
  resetFields,
});
</script>


<template>
  <Form
    ref="formRef"
    class="pima-form"
    label-position="top"
    :model="model"
    :rules="rules"
  >
    <Row
      :gutter="24"
      class="pl-15"
    >
      <!-- 岗位名称 -->
      <Col :span="12">
        <FormItem
          prop="trainingBaseId"
          :label="t('label.postName')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.name"
            :placeholder="t('placeholder.pleaseInput')"
          />
        </FormItem>
      </Col>

      <!-- 岗位类型 -->
      <Col :span="12">
        <FormItem
          prop="trainingBaseId"
          :label="t('label.postType')"
          class="no-colon"
        >
          <SelectPostType
            v-model="model.name"
            :placeholder="t('placeholder.pleaseSelect')"
          />
        </FormItem>
      </Col>

      <!-- 研究方向 -->
      <Col :span="24">
        <FormItem
          prop="trainingBaseId"
          :label="t('label.researchDirection')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.name"
            :placeholder="t('placeholder.pleaseInput')"
          />
        </FormItem>
      </Col>

      <!-- 性别要求 -->
      <Col :span="12">
        <FormItem
          prop="sexRemand"
          :label="t('label.sexRemand')"
          class="no-colon"
        >
          <RadioSex
            v-model="model.name"
          />
        </FormItem>
      </Col>

      <!-- 所需专业 -->
      <Col :span="12">
        <FormItem
          prop="neededMajor"
          :label="t('label.neededMajor')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.name"
            :placeholder="t('placeholder.pleaseInput')"
          />
        </FormItem>
      </Col>

      <!-- 需求人数 -->
      <Col :span="12">
        <FormItem
          prop="numOfPerson"
          :label="t('label.numOfPerson')"
          class="no-colon"
        >
          <NumberInput
            v-model="model.name"
            :style="{width: '100%'}"
            :placeholder="t('placeholder.pleaseInput')"
          />
        </FormItem>
      </Col>

      <!-- 工作内容 -->
      <Col :span="24">
        <FormItem
          prop="jobContent"
          :label="t('label.jobContent')"
          class="no-colon"
        >
          <PimaInput
            v-show="false"
            v-model.trim="model.name"
          />

          <PimaRemoteEditor
            v-model="model.name"
            :height="250"
            :placeholder="t('placeholder.pleaseInput')"
          />
        </FormItem>
      </Col>

      <!-- 实践地址 -->
      <Col :span="24">
        <FormItem
          prop="practiceAddress"
          :label="t('label.practiceAddress')"
          class="no-colon"
        >
          <CascaderRegions
            v-model="model.name1"
            :placeholder="t('placeholder.pleaseSelect')"
          />
        </FormItem>
      </Col>

      <!-- 详细地址 -->
      <Col :span="24">
        <FormItem
          prop="detailedAddress"
          :label="t('label.detailedAddress')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.name"
            :placeholder="t('placeholder.pleaseInput')"
          />
        </FormItem>
      </Col>

      <!-- 报名截止时间 -->
      <Col :span="12">
        <FormItem
          prop="deadline"
          :label="t('label.deadline')"
          class="no-colon"
        >
          <SelectDate
            v-model="model.deadline"
            :disabled-date="disabledDate"
            class="full-width"
          />
        </FormItem>
      </Col>

      <!-- 其他要求 -->
      <Col :span="24">
        <FormItem
          prop="otherRemand"
          :label="t('label.otherRemand')"
          class="no-colon"
        >
          <PimaInput
            v-model.trim="model.name"
            :type="InputType.TEXTAREA"
            :maxlength="255"
            :rows="4"
            show-word-limit
            :placeholder="t('placeholder.pleaseInput')"
          />
        </FormItem>
      </Col>

      <!-- 发布类型 -->
      <Col :span="24">
        <FormItem
          prop="publishType"
          :label="t('label.publishType')"
          class="no-colon"
        >
          <Row class="full-width">
            <Col :span="12">
              <RadioPublishType
                v-model="model.publishType"
              />
            </Col>

            <Col :span="12">
              <SelectDate
                v-if="model.publishType === PublishType.Timing"
                v-model="model.publishTime"
                :disabled-date="disabledDate"
                class="full-width"
              />
            </Col>
          </Row>
        </FormItem>
      </Col>
    </Row>
  </Form>
</template>
