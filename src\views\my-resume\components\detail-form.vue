<script setup lang="ts">
import { onBeforeMount } from 'vue';

import DetailLabelItem from '@/components/common/detail-label-item.vue';
import ShowFile from '@/components/biz/attachment/show-file.vue';

import { namespaceT } from '@/helps/namespace-t';
import { usePostTypeStore } from '@/store/data-tags/post-type';


const model = defineModel<Record<string, any>>();

const t = namespaceT('myResume');
const postTypeStore = usePostTypeStore();


onBeforeMount(() => {
  postTypeStore.loadDataIfNeeded();
});

</script>


<template>
  <Row
    :gutter="24"
    class="pl-20"
  >
    <!-- 学号-->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.sn')"
        :value="model.name"
      />
    </Col>

    <!-- 姓名 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.name')"
        :value="model.name"
      />
    </Col>

    <!-- 院系 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.collage')"
        :value="model.name"
      />
    </Col>

    <!-- 班级 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.class')"
        :value="model.name"
      />
    </Col>

    <!-- 手机 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.phone')"
        :value="model.name"
      />
    </Col>

    <!-- 邮箱 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.email')"
        :value="model.name"
      />
    </Col>

    <!-- 性别 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.sex')"
        :value="model.name"
      />
    </Col>

    <!-- 本科院校 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.graduateSchool')"
        :value="model.name"
      />
    </Col>

    <!-- 本科专业 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.graduateMajor')"
        :value="model.name"
      />
    </Col>

    <!-- 硕士院校 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.masterSchool')"
        :value="model.name"
      />
    </Col>

    <!-- 硕士专业 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.masterMajor')"
        :value="model.name"
      />
    </Col>

    <!-- 实习经历 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.internshipExperience')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 科研经历 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.researchExperience')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 奖励和荣誉 -->

    <Col :span="24">
      <DetailLabelItem
        :label="t('label.awardsAndHonors')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 其他 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.other')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 附件 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.annex')"
        column-flex
      >
        <ShowFile
          :attachments="model.attachments||[]"
          multiple
        />
      </DetailLabelItem>
    </Col>
  </Row>
</template>
