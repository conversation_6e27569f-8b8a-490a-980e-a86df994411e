<script setup lang="ts">
import type { AttachmentVO } from '^/types/attachment';

import DefaultText from '@/components/common/default-text.vue';
import File from './file.vue';


interface Props {
  attachments?: AttachmentVO[]
  multiple?:boolean
}

withDefaults(defineProps<Props>(), {
  attachments: () => [],
  multiple: false,
});


</script>


<template>
  <template v-if="attachments && attachments.length > 0">
    <div
      v-if="!multiple"
      class="single-file"
    >
      <File
        :href="attachments[0].filePath.normal"
        :file-name="attachments[0].fileName"
      />
    </div>

    <div
      v-else
      class="multiple-files"
    >
      <File
        v-for="attachment in attachments"
        :key="attachment.id"
        :href="attachment.filePath.normal"
        :file-name="attachment.fileName"
      />
    </div>
  </template>

  <DefaultText
    v-else
    :text="undefined"
  />
</template>


<style lang="less" scoped>
.multiple-files{
  display: flex;
  flex-direction: column;
  gap:10px;

}
</style>
