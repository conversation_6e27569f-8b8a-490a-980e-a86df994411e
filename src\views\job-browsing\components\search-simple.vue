<script setup lang="ts">
import { inject } from 'vue';

import type { SearchSimpleModelType } from '^/types/job-browsing';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import SelectPostType from '@/components/biz/select/post-type.vue';

import { MENU_NAME_INJECTION_KEY, SiderMenuCodes } from '@/config/sider-menu';
import { namespaceT } from '@/helps/namespace-t';


const emit = defineEmits<{
  'on-search': []
}>();

const t = namespaceT('jobBrowsing');
const model = defineModel<SearchSimpleModelType>();
const menuName = inject<GetMenuName>(MENU_NAME_INJECTION_KEY);


const emitSearch = () => {
  emit('on-search');
};
</script>


<template>
  <WrapperSearchSimple :title="menuName((SC:typeof SiderMenuCodes) => SC.JobBrowsing)">
    <PairLabelItem>
      <SelectPostType
        v-model="model.postType"
        class="w-160 simple-search"
        clearable
        :placeholder="t('placeholder.postType')"
        @on-change="emitSearch"
      />
    </PairLabelItem>


    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="model.keyword"
        :placeholder="t('placeholder.keyword')"
        class="w-450"
        clearable
        @on-clear="emitSearch"
        @on-search="emitSearch"
      />
    </PairLabelItem>
  </WrapperSearchSimple>
</template>
