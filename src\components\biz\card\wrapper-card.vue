
<template>
  <div class="wrapper-card">
    <div
      v-if="$slots.header"
      class="header mb-12"
    >
      <slot name="header" />
    </div>

    <div
      class="content"
    >
      <slot />
    </div>


    <div
      v-if="$slots.footer"
      class="footer"
    >
      <slot name="footer" />
    </div>
  </div>
</template>


<style lang="less" scoped>
.wrapper-card {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 385px;
  height: auto;
  padding: 24px 24px 20px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 1px 5px 0 #06488B12;
  cursor: pointer;

  .content{
    flex: 1;
  }

  .footer{
    margin-top:24px;
    padding-top:20px;
    border-top: 1px solid #f5f5f5;
  }

  // 水平标签容器，支持省略号
  .tags-container {
    display: flex;
    overflow: hidden;
    gap: 8px;
    position: relative;

    // 渐变遮罩效果
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 40px;
      background: linear-gradient(to right, transparent, #fff);
      pointer-events: none;
    }

    // 当容器溢出时显示省略号
    &.has-overflow::after {
      content: '...';
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
      color: #666;
      font-weight: bold;
      width: 30px;
      pointer-events: auto;
    }
  }

  // 标签项样式
  .tag-item {
    flex-shrink: 0;
    padding: 4px 12px;
    background: #f0f0f0;
    border-radius: 4px;
    white-space: nowrap;
    font-size: 14px;
    color: #333;

    // 最后几个可能被遮挡的项
    &:nth-last-child(-n+2) {
      margin-right: 40px;
    }
  }
}

</style>
