
<template>
  <div class="wrapper-card">
    <div
      v-if="$slots.header"
      class="header mb-12"
    >
      <slot name="header" />
    </div>

    <div
      class="content"
    >
      <slot />
    </div>


    <div
      v-if="$slots.footer"
      class="footer"
    >
      <slot name="footer" />
    </div>
  </div>
</template>


<style lang="less" scoped>
.wrapper-card {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 385px;
  height: auto;
  padding: 24px 24px 20px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 1px 5px 0 #06488B12;
  cursor: pointer;

  .content{
    flex: 1;
  }

  .footer{
    margin-top:24px;
    padding-top:20px;
    border-top: 1px solid #f5f5f5;
  }
}

</style>
