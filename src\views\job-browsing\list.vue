<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">
import { onActivated } from 'vue';
import { useRouter } from 'vue-router';

import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import SearchSimple from './components/search-simple.vue';
import CardList from './components/card-list.vue';

import { ListApi } from '@/api/job-post/list';
import { RouterName as RN } from '@/config/router';
import { push } from '@/helps/navigation';
import { useQueryTable } from '@/uses/query-table';
import { useTableLoader } from '@/uses/table-loader';

import { createSearchSimpleModel } from './helps/models';


defineOptions({
  name: 'JobBrowsingList',
});

const router = useRouter();


const loadData = useTableLoader(ListApi);
const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSearchSimpleModel(),
});


const onView = (id:number) => {
  push(router, {
    name: RN.JobBrowsingDetail,
    params: {
      id,
    },
  });
};

onActivated(() => {
  qt.load();
});

</script>

<template>
  <SearchSimple
    v-model="qt.simpleSearchModel"
    @on-search="qt.search"
  />

  <TableScroll>
    <CardList />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>
</template>
