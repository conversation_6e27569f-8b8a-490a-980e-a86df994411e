<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">
import { mockJobPostData } from '../helps/mock-data';
import ContentCard from './card.vue';

interface Props {
  data?:any[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => mockJobPostData,
});

const emit = defineEmits<{
  'on-detail':[id:number]
}>();

</script>


<template>
  <div class="card-list">
    <ContentCard
      v-for="item in data"
      :key="item.id"
      :content="item"
      @click="emit('on-detail',item.id)"
    />
  </div>
</template>


<style lang="less" scoped>
.card-list{
  display: flex;
  flex-wrap: wrap;
  gap:16px;
  width:100%;
  margin:0 24px;
}
</style>
